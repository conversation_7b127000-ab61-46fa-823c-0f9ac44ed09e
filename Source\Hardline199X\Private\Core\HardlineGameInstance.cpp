#include "Core/HardlineGameInstance.h"
#include "Core/HardlineGameManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

UHardlineGameInstance::UHardlineGameInstance()
{
    // Initialize default values
    CurrentGameState = EGameState::MainMenu;
    SaveSlotName = TEXT("HardlineSave");
    SaveUserIndex = 0;
    GraphicsQuality = 3;
    MasterVolume = 1.0f;
    SFXVolume = 1.0f;
    MusicVolume = 0.7f;
}

void UHardlineGameInstance::Init()
{
    Super::Init();
    
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Initializing..."));
    
    InitializeGameManager();
    LoadDefaultSettings();
    
    // Set initial game state
    SetGameState(EGameState::MainMenu);
}

void UHardlineGameInstance::Shutdown()
{
    if (GameManager)
    {
        GameManager->Shutdown();
    }
    
    SaveSettings();
    
    Super::Shutdown();
}

void UHardlineGameInstance::InitializeGameManager()
{
    if (!GameManager)
    {
        GameManager = NewObject<UHardlineGameManager>(this);
        if (GameManager)
        {
            GameManager->Initialize();
            UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Game Manager initialized"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("HardlineGameInstance: Failed to create Game Manager"));
        }
    }
}

void UHardlineGameInstance::SetGameState(EGameState NewState)
{
    if (CurrentGameState != NewState)
    {
        EGameState PreviousState = CurrentGameState;
        CurrentGameState = NewState;
        
        UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Game state changed from %d to %d"), 
               (int32)PreviousState, (int32)NewState);
        
        // Broadcast the state change
        OnGameStateChanged.Broadcast(NewState);
        
        // Handle state-specific logic
        switch (NewState)
        {
            case EGameState::Playing:
                if (GameManager)
                {
                    GameManager->StartLevel();
                }
                break;
                
            case EGameState::Paused:
                UGameplayStatics::SetGamePaused(GetWorld(), true);
                break;
                
            case EGameState::GameOver:
            case EGameState::Victory:
                if (GameManager)
                {
                    GameManager->EndLevel(NewState == EGameState::Victory);
                }
                break;
                
            default:
                if (PreviousState == EGameState::Paused)
                {
                    UGameplayStatics::SetGamePaused(GetWorld(), false);
                }
                break;
        }
    }
}

void UHardlineGameInstance::LoadLevel(const FString& LevelName)
{
    if (!LevelName.IsEmpty())
    {
        CurrentLevelName = LevelName;
        SetGameState(EGameState::Loading);
        
        UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Loading level %s"), *LevelName);
        
        UGameplayStatics::OpenLevel(GetWorld(), FName(*LevelName));
    }
}

void UHardlineGameInstance::RestartCurrentLevel()
{
    if (!CurrentLevelName.IsEmpty())
    {
        ResetPlayerStats();
        LoadLevel(CurrentLevelName);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("HardlineGameInstance: No current level to restart"));
    }
}

void UHardlineGameInstance::ReturnToMainMenu()
{
    SetGameState(EGameState::MainMenu);
    LoadLevel(TEXT("MainMenu"));
}

void UHardlineGameInstance::UpdatePlayerStats(const FPlayerStats& NewStats)
{
    PlayerStats = NewStats;
}

void UHardlineGameInstance::ResetPlayerStats()
{
    PlayerStats = FPlayerStats();
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Player stats reset"));
}

void UHardlineGameInstance::SaveGame()
{
    // TODO: Implement save game functionality
    // This would save player progress, settings, and statistics
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Saving game..."));
}

void UHardlineGameInstance::LoadGame()
{
    // TODO: Implement load game functionality
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Loading game..."));
}

bool UHardlineGameInstance::DoesSaveGameExist() const
{
    return UGameplayStatics::DoesSaveGameExist(SaveSlotName, SaveUserIndex);
}

void UHardlineGameInstance::ApplyGraphicsSettings(int32 QualityLevel)
{
    GraphicsQuality = FMath::Clamp(QualityLevel, 0, 4);
    
    // Apply graphics settings
    // TODO: Implement actual graphics settings application
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Applied graphics quality level %d"), GraphicsQuality);
}

void UHardlineGameInstance::ApplyAudioSettings(float MasterVol, float SFXVol, float MusicVol)
{
    MasterVolume = FMath::Clamp(MasterVol, 0.0f, 1.0f);
    SFXVolume = FMath::Clamp(SFXVol, 0.0f, 1.0f);
    MusicVolume = FMath::Clamp(MusicVol, 0.0f, 1.0f);
    
    // Apply audio settings
    // TODO: Implement actual audio settings application
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Applied audio settings - Master: %f, SFX: %f, Music: %f"), 
           MasterVolume, SFXVolume, MusicVolume);
}

void UHardlineGameInstance::LoadDefaultSettings()
{
    // TODO: Load settings from saved preferences
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Loading default settings"));
}

void UHardlineGameInstance::SaveSettings()
{
    // TODO: Save current settings to persistent storage
    UE_LOG(LogTemp, Log, TEXT("HardlineGameInstance: Saving settings"));
}
