# MetaSounds — THUMPER-12 Shotgun (procedural layer stack)

Create `/Game/Audio/MS_Thumper12`. This graph builds a heavy transient, a body thump, and a short tail with slight randomization.

## Parameters (Expose as Inputs)
- `RandomPitch` (float, default 0.0, range ±0.06)
- `BodyGain` (linear, default 0.8)
- `TailMs` (float, default 180)

## Node-by-Node
1. **OnPlay** → **Trigger** (start)
2. **Random Float Range** (−RandomPitch .. +RandomPitch) → **Pitch Shifter** (modest, cents)
3. **Transient Layer**
   - **OneShot Player** (short click sample or Noise → Env ADSR: A=0.001, D=0.025, S=0, R=0.03)
   - **HighPass Filter** (~800 Hz) then **Saturator** (light clip)
4. **Body Layer**
   - **Sine/Saw Osc** at 75–95 Hz mixed with **Pink Noise**
   - **Env ADSR**: A=0.002, D=0.12, S=0.0, R=0.18
   - **LowPass** at 2.2 kHz, **Gain** multiplied by `BodyGain`
5. **Tail Layer**
   - **Convolution Reverb** (small room IR) or **Feedback Delay** (ms = `TailMs`, feedback 0.28)
   - **HighCut** at ~6–7 kHz
6. **Mixer**
   - Sum Transient + Body + Tail → **Output** (Mono to Stereo Upmix if needed)
7. **Variations**
   - Add slight **Start Time Random** for a breath of uniqueness
   - Randomize Body Osc frequency ±5 Hz per shot

Bind this MetaSound in your weapon fire event. For alt-fire, bump BodyGain + TailMs slightly for extra oomph.
