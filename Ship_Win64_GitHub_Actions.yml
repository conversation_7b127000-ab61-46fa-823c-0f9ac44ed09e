name: Ship-Win64
on:
  workflow_dispatch:
jobs:
  package:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Package Shipping
        shell: pwsh
        run: |
          $UE="C:\Program Files\Epic Games\UE_5.4"
          $PROJ="${{ github.workspace }}\Hardline199X.uproject"
          $OUT="${{ github.workspace }}\_Artifacts"
          & "$UE\Engine\Build\BatchFiles\RunUAT.bat" BuildCookRun `
            -project="$PROJ" `
            -noP4 -platform=Win64 -clientconfig=Shipping `
            -build -cook -allmaps -stage -iostore -archive -archivedirectory="$OUT" -prereqs -CrashReporter
      - uses: actions/upload-artifact@v4
        with:
          name: Hardline199X_Win64_Shipping
          path: _Artifacts
