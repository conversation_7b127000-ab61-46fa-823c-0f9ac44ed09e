# Hardline199X Blueprint Setup Guide

## Overview
This guide provides step-by-step instructions for setting up all Blueprint assets in Hardline199X, organized by system priority for parallel development.

## Phase 1: Core Foundation Blueprints

### 1. Game Instance Setup
**Location**: `/Game/Blueprints/Core/BP_HardlineGameInstance`
**Parent Class**: `HardlineGameInstance` (C++)

**Setup Steps**:
1. Create Blueprint based on `UHardlineGameInstance`
2. Set as Project Settings → Game Instance Class
3. Configure default values:
   - Graphics Quality: 3
   - Master Volume: 1.0
   - SFX Volume: 1.0
   - Music Volume: 0.7

### 2. Game Mode Setup
**Location**: `/Game/Blueprints/Core/BP_HardlineGameMode`
**Parent Class**: `GameModeBase`

**Setup Steps**:
1. Create GameMode Blueprint
2. Set Default Classes:
   - Default Pawn Class: `BP_HardlineCharacter`
   - HUD Class: `BP_HardlineHUD`
   - Player Controller Class: `BP_HardlinePlayerController`
3. Set in Project Settings → Maps & Modes

### 3. Player Controller Setup
**Location**: `/Game/Blueprints/Core/BP_HardlinePlayerController`
**Parent Class**: `PlayerController`

**Setup Steps**:
1. Create PlayerController Blueprint
2. Enable Enhanced Input
3. Set Input Mode to Game Only
4. Configure mouse sensitivity settings

## Phase 2: Player System Blueprints

### 1. Player Character
**Location**: `/Game/Blueprints/Player/BP_HardlineCharacter`
**Parent Class**: `HardlineCharacter` (C++)

**Components to Add**:
- First Person Camera (already in C++)
- Health Component (already in C++)
- Inventory Component (already in C++)
- Audio Component (for footsteps)

**Setup Steps**:
1. Create Blueprint based on `AHardlineCharacter`
2. Configure mesh and collision
3. Set up Enhanced Input Mapping Context
4. Configure movement speeds:
   - Walk Speed: 400
   - Sprint Speed: 600
   - Crouch Speed: 200
5. Add weapon socket to mesh
6. Set up audio components for footsteps

### 2. Enhanced Input Setup
**Location**: `/Game/Input/`

**Assets to Create**:
- `IMC_Default` (Input Mapping Context)
- `IA_Move` (Input Action - Vector2D)
- `IA_Look` (Input Action - Vector2D)
- `IA_Jump` (Input Action - Boolean)
- `IA_Crouch` (Input Action - Boolean)
- `IA_Sprint` (Input Action - Boolean)
- `IA_Fire` (Input Action - Boolean)
- `IA_AltFire` (Input Action - Boolean)
- `IA_Reload` (Input Action - Boolean)
- `IA_WeaponSwitch` (Input Action - Axis1D)
- `IA_Interact` (Input Action - Boolean)

**Key Bindings**:
```
Move: WASD, Left Stick
Look: Mouse, Right Stick
Jump: Space, Gamepad Face Button Bottom
Crouch: Left Ctrl, Gamepad Right Stick
Sprint: Left Shift, Gamepad Left Stick
Fire: Left Mouse, Right Trigger
Alt Fire: Right Mouse, Left Trigger
Reload: R, Gamepad X
Weapon Switch: Mouse Wheel, Gamepad Shoulder Buttons
Interact: E, Gamepad Y
```

## Phase 3: Weapon System Blueprints

### 1. Base Weapon Blueprint
**Location**: `/Game/Blueprints/Weapons/BP_HardlineWeapon`
**Parent Class**: `HardlineWeapon` (C++)

**Setup Steps**:
1. Create Blueprint based on `AHardlineWeapon`
2. Configure weapon mesh and materials
3. Set up muzzle location component
4. Configure default weapon stats
5. Add audio and particle effect references

### 2. THUMPER-12 Shotgun
**Location**: `/Game/Blueprints/Weapons/BP_Thumper12`
**Parent Class**: `BP_HardlineWeapon`

**Configuration**:
```
Weapon Stats:
- Damage: 80.0
- Fire Rate: 120 (RPM)
- Range: 800.0
- Accuracy: 0.85
- Magazine Size: 8
- Reload Time: 2.5
- Weapon Type: Shotgun
- Damage Type: Ballistic
```

**Special Features**:
- Multi-pellet firing pattern
- Alt-fire for slug rounds
- Pump-action animation
- MetaSound integration (MS_Thumper12)

### 3. Additional Weapons (Future)
**Locations**:
- `/Game/Blueprints/Weapons/BP_Pistol`
- `/Game/Blueprints/Weapons/BP_AssaultRifle`
- `/Game/Blueprints/Weapons/BP_MeleeWeapon`

## Phase 4: UI System Blueprints

### 1. HUD Widget
**Location**: `/Game/UI/HUD/WBP_MainHUD`
**Parent Class**: `UserWidget`

**Components**:
- Health Bar (Progress Bar)
- Ammo Counter (Text)
- Crosshair (Image)
- Objective Tracker (Vertical Box)
- Damage Indicator (Overlay)

**Binding Setup**:
1. Bind Health Bar to Player Health Component
2. Bind Ammo Counter to Current Weapon
3. Update crosshair based on weapon type
4. Connect to Game Manager for objectives

### 2. Main Menu
**Location**: `/Game/UI/Menus/WBP_MainMenu`
**Parent Class**: `UserWidget`

**Components**:
- Background Image (Cyberpunk theme)
- Title Text
- Play Button
- Settings Button
- Quit Button
- Version Text

### 3. Pause Menu
**Location**: `/Game/UI/Menus/WBP_PauseMenu`
**Parent Class**: `UserWidget`

**Components**:
- Resume Button
- Settings Button
- Main Menu Button
- Quit Button
- Background Blur

### 4. Settings Menu
**Location**: `/Game/UI/Menus/WBP_SettingsMenu`
**Parent Class**: `UserWidget`

**Tabs**:
- Graphics Settings
- Audio Settings
- Controls Settings
- Gameplay Settings

## Phase 5: AI System Blueprints

### 1. Goonbot Enemy
**Location**: `/Game/Blueprints/AI/BP_Goonbot`
**Parent Class**: `Character`

**Components**:
- AI Controller: `BP_AC_Goonbot`
- Behavior Tree: `BT_Goonbot`
- Blackboard: `BB_Goonbot`
- Health Component
- Weapon Component

**Setup Steps**:
1. Configure mesh and animations
2. Set up AI Perception (Sight + Hearing)
3. Configure weapon attachment
4. Set health and damage values
5. Add death/hit reaction animations

### 2. AI Controller
**Location**: `/Game/Blueprints/AI/BP_AC_Goonbot`
**Parent Class**: `AIController`

**Components**:
- AI Perception Component
- Blackboard Component
- Behavior Tree Component

**Perception Settings**:
- Sight: Radius 2200, FOV 110°
- Hearing: Range 2500
- Max Age: 5 seconds

## Phase 6: Level and PCG Blueprints

### 1. PCG Spline Setup
**Location**: `/Game/PCG/BP_PCG_NeonSpillway`
**Parent Class**: `PCGVolume`

**Setup Steps**:
1. Create PCG Volume
2. Assign PCG Graph: `PCG_NeonSpillway`
3. Configure spline actors with `PCG_Spline` tag
4. Set up prop and loot spawn collections

### 2. Interactive Objects
**Location**: `/Game/Blueprints/Interactive/`

**Objects to Create**:
- `BP_HealthPack` (pickup)
- `BP_AmmoBox` (pickup)
- `BP_Terminal` (hackable)
- `BP_DestructibleCover` (environmental)

## Phase 7: Effects and Audio Blueprints

### 1. Particle Systems
**Location**: `/Game/Effects/Particles/`

**Systems to Create**:
- `P_MuzzleFlash_Shotgun`
- `P_BulletImpact_Concrete`
- `P_BulletImpact_Metal`
- `P_BloodSplatter`
- `P_ShellEjection`

### 2. MetaSound Assets
**Location**: `/Game/Audio/MetaSounds/`

**Assets**:
- `MS_Thumper12` (shotgun audio)
- `MS_Footsteps` (player movement)
- `MS_AmbientCity` (environmental)

## Blueprint Integration Patterns

### 1. Event Communication
Use Blueprint Interfaces for system communication:
- `BPI_Damageable` (for health systems)
- `BPI_Interactable` (for pickup/interaction)
- `BPI_WeaponUser` (for weapon wielding)

### 2. Component Architecture
Standardize component usage:
- Health Component on all damageable actors
- Inventory Component on player and AI
- Weapon Component for weapon management

### 3. Data Binding
Use proper data binding patterns:
- UI widgets bind to component events
- Game Manager coordinates system updates
- Save system preserves component states

## Testing and Validation

### 1. Component Testing
- Test each Blueprint independently
- Verify component interactions
- Validate event broadcasting

### 2. System Integration
- Test player + weapon + UI integration
- Verify AI + player + objective integration
- Test save/load with all systems

### 3. Performance Validation
- Profile Blueprint execution
- Optimize tick functions
- Validate memory usage

## Common Pitfalls and Solutions

### 1. Circular References
**Problem**: Blueprints referencing each other directly
**Solution**: Use interfaces and event dispatchers

### 2. Performance Issues
**Problem**: Too many tick functions
**Solution**: Use timers and event-driven updates

### 3. Save/Load Problems
**Problem**: Complex object references not saving
**Solution**: Use component-based save data structures

This guide provides the foundation for implementing all Hardline199X systems in parallel while maintaining proper integration patterns.
