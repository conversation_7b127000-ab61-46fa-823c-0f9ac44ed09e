#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Core/HardlineTypes.h"
#include "HardlineHealthComponent.generated.h"

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class HARDLINE199X_API UHardlineHealthComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UHardlineHealthComponent();

protected:
    virtual void BeginPlay() override;

public:
    // Health Management
    UFUNCTION(BlueprintCallable, Category = "Health")
    void TakeDamage(float DamageAmount, EDamageType DamageType = EDamageType::Ballistic, AActor* DamageSource = nullptr);

    UFUNCTION(BlueprintCallable, Category = "Health")
    void Heal(float HealAmount);

    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetHealth(float NewHealth);

    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetMaxHealth(float NewMaxHealth);

    UFUNCTION(BlueprintCallable, Category = "Health")
    void ResetHealth();

    // Health Queries
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Health")
    float GetHealth() const { return CurrentHealth; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Health")
    float GetMaxHealth() const { return MaxHealth; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Health")
    float GetHealthPercentage() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Health")
    bool IsAlive() const { return CurrentHealth > 0.0f; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Health")
    bool IsAtFullHealth() const { return CurrentHealth >= MaxHealth; }

    // Damage Resistance
    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetDamageResistance(EDamageType DamageType, float Resistance);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Health")
    float GetDamageResistance(EDamageType DamageType) const;

    // Regeneration
    UFUNCTION(BlueprintCallable, Category = "Health")
    void StartHealthRegeneration();

    UFUNCTION(BlueprintCallable, Category = "Health")
    void StopHealthRegeneration();

    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetRegenerationRate(float NewRate) { RegenerationRate = NewRate; }

    // Event Delegates
    UPROPERTY(BlueprintAssignable, Category = "Health Events")
    FOnHealthChanged OnHealthChanged;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnDamageTaken, float, DamageAmount, EDamageType, DamageType, AActor*, DamageSource);
    UPROPERTY(BlueprintAssignable, Category = "Health Events")
    FOnDamageTaken OnDamageTaken;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDeath);
    UPROPERTY(BlueprintAssignable, Category = "Health Events")
    FOnDeath OnDeath;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnHealed, float, HealAmount);
    UPROPERTY(BlueprintAssignable, Category = "Health Events")
    FOnHealed OnHealed;

protected:
    // Health Properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health", meta = (ClampMin = "0.0"))
    float MaxHealth = 100.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Health")
    float CurrentHealth = 100.0f;

    // Damage Resistance (0.0 = no resistance, 1.0 = immune)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Resistance")
    TMap<EDamageType, float> DamageResistances;

    // Regeneration Properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    bool bCanRegenerate = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationRate = 5.0f; // Health per second

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationDelay = 3.0f; // Seconds after taking damage

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationThreshold = 0.8f; // Stop regen at this percentage of max health

    // Internal State
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bIsRegenerating = false;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bIsDead = false;

    // Timers
    FTimerHandle RegenerationTimerHandle;
    FTimerHandle RegenerationDelayTimerHandle;

private:
    void HandleDeath();
    void RegenerateHealth();
    void StartRegenerationTimer();
    float CalculateFinalDamage(float BaseDamage, EDamageType DamageType) const;
};
