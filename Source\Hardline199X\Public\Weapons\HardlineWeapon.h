#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Core/HardlineTypes.h"
#include "HardlineWeapon.generated.h"

class USkeletalMeshComponent;
class UStaticMeshComponent;
class USceneComponent;
class USoundBase;
class UParticleSystem;
class UAnimMontage;
class AHardlineCharacter;

UCLASS(BlueprintType, Blueprintable)
class HARDLINE199X_API AHardlineWeapon : public AActor
{
    GENERATED_BODY()

public:
    AHardlineWeapon();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Weapon Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USkeletalMeshComponent> WeaponMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> MuzzleLocation;

    // Weapon Stats
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    FWeaponStats WeaponStats;

    // Ammo System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    int32 CurrentAmmo = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    int32 MaxAmmo = 300;

    // Firing
    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void StartFire();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void StopFire();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void StartAltFire();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void StopAltFire();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void Reload();

    // Weapon State
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
    bool CanFire() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
    bool CanReload() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
    bool IsReloading() const { return bIsReloading; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
    bool IsFiring() const { return bIsFiring; }

    // Ammo Management
    UFUNCTION(BlueprintCallable, Category = "Ammo")
    void AddAmmo(int32 AmmoAmount);

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    void SetAmmo(int32 NewAmmo);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ammo")
    int32 GetCurrentAmmo() const { return CurrentAmmo; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ammo")
    int32 GetMaxAmmo() const { return MaxAmmo; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ammo")
    int32 GetMagazineAmmo() const { return MagazineAmmo; }

    // Owner Management
    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void SetWeaponOwner(AHardlineCharacter* NewOwner);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
    AHardlineCharacter* GetWeaponOwner() const { return WeaponOwner; }

    // Weapon Type
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
    EWeaponType GetWeaponType() const { return WeaponStats.WeaponType; }

    // Event Delegates
    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnAmmoChanged OnAmmoChanged;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponFired, AHardlineWeapon*, Weapon);
    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnWeaponFired OnWeaponFired;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponReloaded, AHardlineWeapon*, Weapon);
    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnWeaponReloaded OnWeaponReloaded;

protected:
    // Owner Reference
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Owner")
    TObjectPtr<AHardlineCharacter> WeaponOwner;

    // Ammo State
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Ammo")
    int32 MagazineAmmo = 30;

    // Weapon State
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bIsFiring = false;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bIsReloading = false;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bCanFire = true;

    // Audio & Visual Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TObjectPtr<USoundBase> FireSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TObjectPtr<USoundBase> ReloadSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TObjectPtr<UParticleSystem> MuzzleFlash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TObjectPtr<UParticleSystem> ImpactEffect;

    // Animation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    TObjectPtr<UAnimMontage> FireAnimation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    TObjectPtr<UAnimMontage> ReloadAnimation;

    // Timers
    FTimerHandle FireRateTimerHandle;
    FTimerHandle ReloadTimerHandle;

    // Virtual Functions for Subclasses
    virtual void FireWeapon();
    virtual void PerformReload();
    virtual void PlayFireEffects();
    virtual void PlayReloadEffects();
    virtual FVector GetFireDirection() const;
    virtual void ProcessHit(const FHitResult& HitResult);

private:
    void OnFireRateTimer();
    void OnReloadComplete();
    void ConsumeAmmo();
    void UpdateAmmoDisplay();
};
