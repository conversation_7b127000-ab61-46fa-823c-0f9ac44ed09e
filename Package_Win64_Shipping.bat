@echo off
REM =============================================================
REM  Hardline199X — Windows Shipping build (UE5 RunUAT one-click)
REM  EDIT the three paths below before running.
REM =============================================================

set UE_ROOT="C:\Program Files\Epic Games\UE_5.4"
set PROJ="C:\Dev\Hardline199X\Hardline199X.uproject"
set OUT="C:\Builds\Hardline199X_Win64_Shipping"

REM Toggle IoStore vs Pak (UE5 defaults to IoStore). 1 = IoStore, 0 = Pak
set USE_IOSTORE=1
set PAKFLAG=
if %USE_IOSTORE%==1 ( set PAKFLAG=-iostore ) else ( set PAKFLAG=-pak )

REM Optional: list specific maps instead of -allmaps (example below)
REM set MAPS=-map=NeonSpillway+Substation+Tunnels+Dock
set MAPS=-allmaps

echo.
echo === Building, Cooking, and Packaging (Shipping) ===
"%UE_ROOT%\Engine\Build\BatchFiles\RunUAT.bat" BuildCookRun ^
 -project=%PROJ% ^
 -noP4 ^
 -platform=Win64 ^
 -clientconfig=Shipping ^
 -build ^
 -cook %MAPS% ^
 -stage ^
 %PAKFLAG% ^
 -archive -archivedirectory=%OUT% ^
 -prereqs ^
 -CrashReporter

IF %ERRORLEVEL% NEQ 0 (
  echo.
  echo *** BuildCookRun FAILED ***
  exit /b 1
)

echo.
echo SUCCESS.
echo Output at: %OUT%\Windows
