# UE Editor Python: quick project scaffold + Nanite toggle on selected meshes.
# Enable "Python Editor Script Plugin" first, then paste in Window -> Output Log (Python) or run via an Editor Utility Widget.
import unreal as u

def ensure_dirs(paths):
    for p in paths:
        if not u.EditorAssetLibrary.does_directory_exist(p):
            u.EditorAssetLibrary.make_directory(p)

ensure_dirs([
    "/Game/Blueprints","/Game/Weapons","/Game/AI",
    "/Game/Levels","/Game/Audio","/Game/PCG","/Game/Input"
])

sel = u.EditorUtilityLibrary.get_selected_assets()
count = 0
for a in sel:
    if isinstance(a, u.StaticMesh):
        settings = a.get_editor_property("nanite_settings")
        settings.enabled = True
        a.set_editor_property("nanite_settings", settings)
        a.mark_package_dirty()
        count += 1
u.SystemLibrary.print_string(None, f"Nanite enabled on {count} static meshes.")

# Optional: Set default maps if they exist. Update paths as needed.
gms = u.get_default_object(u.GameMapsSettings)
if gms:
    changed=False
    main_map = "/Game/Levels/NeonSpillway"
    if u.EditorAssetLibrary.does_asset_exist(main_map):
        gms.set_editor_property("editor_game_map", main_map)
        gms.set_editor_property("game_default_map", main_map)
        changed=True
    if changed:
        u.EditorAssetLibrary.save_current_level()
        u.SystemLibrary.print_string(None, "Default maps set to NeonSpillway")
