#include "Core/HardlineGameManager.h"
#include "Engine/World.h"
#include "TimerManager.h"

UHardlineGameManager::UHardlineGameManager()
{
    bLevelInProgress = false;
    LevelStartTime = 0.0f;
}

void UHardlineGameManager::Initialize()
{
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Initializing..."));
    
    // Reset all tracking arrays
    ActiveObjectives.Empty();
    ActiveEnemies.Empty();
    EnemyTypeMap.Empty();
    RegisteredWeapons.Empty();
    
    // Reset statistics
    CurrentStats = FPlayerStats();
    
    bLevelInProgress = false;
    LevelStartTime = 0.0f;
}

void UHardlineGameManager::Shutdown()
{
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Shutting down..."));
    
    // Clear all references
    PlayerCharacter = nullptr;
    ActiveObjectives.Empty();
    ActiveEnemies.Empty();
    EnemyTypeMap.Empty();
    RegisteredWeapons.Empty();
}

void UHardlineGameManager::RegisterPlayer(AHardlineCharacter* Player)
{
    if (Player)
    {
        PlayerCharacter = Player;
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Player registered"));
    }
}

void UHardlineGameManager::AddObjective(const FObjectiveData& NewObjective)
{
    ActiveObjectives.Add(NewObjective);
    
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Added objective '%s'"), *NewObjective.ObjectiveName);
    
    // Broadcast objective update
    OnObjectiveUpdated.Broadcast(NewObjective);
}

void UHardlineGameManager::UpdateObjective(const FString& ObjectiveName, int32 Progress)
{
    FObjectiveData* Objective = FindObjective(ObjectiveName);
    if (Objective)
    {
        Objective->CurrentCount = FMath::Min(Progress, Objective->TargetCount);
        
        // Check if objective is now complete
        if (Objective->CurrentCount >= Objective->TargetCount && !Objective->bIsCompleted)
        {
            CompleteObjective(ObjectiveName);
        }
        else
        {
            OnObjectiveUpdated.Broadcast(*Objective);
        }
    }
}

void UHardlineGameManager::CompleteObjective(const FString& ObjectiveName)
{
    FObjectiveData* Objective = FindObjective(ObjectiveName);
    if (Objective && !Objective->bIsCompleted)
    {
        Objective->bIsCompleted = true;
        Objective->CurrentCount = Objective->TargetCount;
        
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Completed objective '%s'"), *ObjectiveName);
        
        // Update statistics
        CurrentStats.ObjectivesCompleted++;
        
        // Broadcast objective completion
        OnObjectiveUpdated.Broadcast(*Objective);
        
        // Check if all objectives are complete
        if (AreAllObjectivesComplete())
        {
            UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: All objectives complete!"));
            // Level completion will be handled by the game instance
        }
    }
}

bool UHardlineGameManager::AreAllObjectivesComplete() const
{
    for (const FObjectiveData& Objective : ActiveObjectives)
    {
        if (!Objective.bIsCompleted && !Objective.bIsOptional)
        {
            return false;
        }
    }
    return ActiveObjectives.Num() > 0; // Must have at least one objective
}

void UHardlineGameManager::RegisterEnemy(AActor* Enemy, EEnemyType EnemyType)
{
    if (Enemy && !ActiveEnemies.Contains(Enemy))
    {
        ActiveEnemies.Add(Enemy);
        EnemyTypeMap.Add(Enemy, EnemyType);
        
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Registered enemy of type %d"), (int32)EnemyType);
    }
}

void UHardlineGameManager::UnregisterEnemy(AActor* Enemy)
{
    if (Enemy)
    {
        ActiveEnemies.Remove(Enemy);
        EnemyTypeMap.Remove(Enemy);
        
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Unregistered enemy"));
    }
}

void UHardlineGameManager::OnEnemyKilled(AActor* Enemy, EEnemyType EnemyType)
{
    if (Enemy)
    {
        // Record the kill
        RecordKill(EnemyType);
        
        // Remove from active enemies
        UnregisterEnemy(Enemy);
        
        // Broadcast enemy killed event
        OnEnemyKilled.Broadcast(EnemyType);
        
        // Update kill-related objectives
        for (FObjectiveData& Objective : ActiveObjectives)
        {
            if (Objective.ObjectiveType == EObjectiveType::KillAll && !Objective.bIsCompleted)
            {
                UpdateObjective(Objective.ObjectiveName, CurrentStats.KillCount);
            }
        }
        
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Enemy killed, %d enemies remaining"), 
               ActiveEnemies.Num());
    }
}

void UHardlineGameManager::RegisterWeapon(AHardlineWeapon* Weapon)
{
    if (Weapon && !RegisteredWeapons.Contains(Weapon))
    {
        RegisteredWeapons.Add(Weapon);
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Weapon registered"));
    }
}

void UHardlineGameManager::UnregisterWeapon(AHardlineWeapon* Weapon)
{
    if (Weapon)
    {
        RegisteredWeapons.Remove(Weapon);
        UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Weapon unregistered"));
    }
}

void UHardlineGameManager::StartLevel()
{
    bLevelInProgress = true;
    LevelStartTime = GetWorld()->GetTimeSeconds();
    
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Level started"));
    
    // Start the level timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            FTimerHandle(),
            this,
            &UHardlineGameManager::UpdateLevelTimer,
            1.0f,
            true
        );
    }
}

void UHardlineGameManager::EndLevel(bool bVictory)
{
    bLevelInProgress = false;
    
    // Calculate final completion time
    if (GetWorld())
    {
        CurrentStats.CompletionTime = GetWorld()->GetTimeSeconds() - LevelStartTime;
    }
    
    // Calculate accuracy
    if (CurrentStats.ShotsFired > 0)
    {
        CurrentStats.AccuracyPercentage = (float)CurrentStats.ShotsHit / (float)CurrentStats.ShotsFired * 100.0f;
    }
    
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Level ended - Victory: %s, Time: %.2f, Accuracy: %.1f%%"), 
           bVictory ? TEXT("Yes") : TEXT("No"), 
           CurrentStats.CompletionTime, 
           CurrentStats.AccuracyPercentage);
}

void UHardlineGameManager::PauseGame()
{
    // Pause logic handled by Game Instance
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Game paused"));
}

void UHardlineGameManager::ResumeGame()
{
    // Resume logic handled by Game Instance
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Game resumed"));
}

void UHardlineGameManager::RecordKill(EEnemyType EnemyType)
{
    CurrentStats.KillCount++;
    UE_LOG(LogTemp, Log, TEXT("HardlineGameManager: Kill recorded - Total: %d"), CurrentStats.KillCount);
}

void UHardlineGameManager::RecordShot(bool bHit)
{
    CurrentStats.ShotsFired++;
    if (bHit)
    {
        CurrentStats.ShotsHit++;
    }
    
    // Update accuracy percentage
    CurrentStats.AccuracyPercentage = (float)CurrentStats.ShotsHit / (float)CurrentStats.ShotsFired * 100.0f;
}

void UHardlineGameManager::CheckLevelCompletion()
{
    // Check various completion conditions
    bool bShouldComplete = false;
    
    // Check if all required objectives are complete
    if (AreAllObjectivesComplete())
    {
        bShouldComplete = true;
    }
    
    // Check if all enemies are dead (for kill-all objectives)
    for (const FObjectiveData& Objective : ActiveObjectives)
    {
        if (Objective.ObjectiveType == EObjectiveType::KillAll && 
            !Objective.bIsCompleted && 
            ActiveEnemies.Num() == 0)
        {
            bShouldComplete = true;
            break;
        }
    }
    
    if (bShouldComplete)
    {
        EndLevel(true);
    }
}

void UHardlineGameManager::UpdateLevelTimer()
{
    if (bLevelInProgress && GetWorld())
    {
        CurrentStats.CompletionTime = GetWorld()->GetTimeSeconds() - LevelStartTime;
    }
}

FObjectiveData* UHardlineGameManager::FindObjective(const FString& ObjectiveName)
{
    for (FObjectiveData& Objective : ActiveObjectives)
    {
        if (Objective.ObjectiveName == ObjectiveName)
        {
            return &Objective;
        }
    }
    return nullptr;
}
