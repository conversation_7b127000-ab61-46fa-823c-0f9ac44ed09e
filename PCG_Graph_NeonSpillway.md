# PCG Graph — Alley Blockout & Prop Scatter (NeonSpillway)

This recipe gives you a repeatable alley → substation → tunnels → dock blockout with cover props and pickups.

## Actors to Place
1. **PCG Volume** covering your play space (rename to `PCG_NeonSpillway`).
2. **Spline** actor named `<PERSON>P<PERSON>` (draw your main route).
3. (Optional) A second **Spline** `SidePath` for a secret vent detour.
4. Floor meshes or landscape for projection.

## PCG Graph (create `/Game/PCG/PCG_NeonSpillway`)
Add these nodes roughly in order (wire execution from top to bottom).

1. **Input** (Type: Actor)
   - Filters → Include Only → tag the `AlleyPath` actor with tag `PCG_Spline` and filter for that tag.
2. **Spline Sampler**
   - Source: from Input
   - Output: Spline Polyline
3. **Distribute Points on Spline**
   - Mode: Distance Between Points (Min 350, Max 500)
   - Random Seed: exposed as `Seed`
4. **Project Points on Surfaces**
   - Direction: (0,0,-1), Max Distance: 5000 (raycast down to floor)
5. **Distance to Neighbors Filter**
   - Keep Minimum Distance: 250 (avoid clustering)
6. **Random Rotation / Scale (Transform Points)**
   - Yaw: Random 0–360
   - Uniform Scale 0.9–1.2
7. **Static Mesh Spawner**
   - Mesh Set: a collection (dumpsters, crates, barrels, AC units)
   - Collision: Block All
   - Tag Output: `CoverProp`

### Loot Pass (second branch)
1. **Input** (same Spline) → **Distribute Points on Spline** (Distance 600–800)
2. **Project Points on Surfaces** (downward)
3. **Distance from Tagged Points Filter** (avoid cover)
   - Exclude where Distance to `CoverProp` < 300
4. **Static Mesh / Blueprint Spawner** → Place ammo packs & health stims
   - Tag Output: `LootSpawn`

### Walls/Floors Tiler (optional third branch)
1. **Input** (Volume) → **Grid Sampler** (cell size 400)
2. **Filter by Mask** (use a weightmap/landscape layer or custom spline area) to keep corridor cells
3. **Static Mesh Spawner** → place wall/floor modules; rotate to align with spline tangent

> Move the `AlleyPath` spline points and hit **Regenerate** on the PCG Volume to quickly iterate your layout.
