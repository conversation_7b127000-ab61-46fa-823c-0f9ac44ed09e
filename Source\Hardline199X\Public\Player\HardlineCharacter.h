#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "InputActionValue.h"
#include "Core/HardlineTypes.h"
#include "HardlineCharacter.generated.h"

class UCameraComponent;
class UInputMappingContext;
class UInputAction;
class UHardlineHealthComponent;
class UHardlineInventoryComponent;
class AHardlineWeapon;

UCLASS(BlueprintType, Blueprintable)
class HARDLINE199X_API AHardlineCharacter : public ACharacter
{
    GENERATED_BODY()

public:
    AHardlineCharacter();

protected:
    virtual void BeginPlay() override;
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

public:
    virtual void Tick(float DeltaTime) override;

    // Camera and Movement
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera")
    TObjectPtr<UCameraComponent> FirstPersonCamera;

    // Enhanced Input
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputMappingContext> DefaultMappingContext;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> MoveAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> LookAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> JumpAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> CrouchAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> SprintAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> FireAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> AltFireAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> ReloadAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> WeaponSwitchAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> InteractAction;

    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UHardlineHealthComponent> HealthComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UHardlineInventoryComponent> InventoryComponent;

    // Movement Properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float WalkSpeed = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float SprintSpeed = 600.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float CrouchSpeed = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float MouseSensitivity = 1.0f;

    // Current Weapon
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Weapons")
    TObjectPtr<AHardlineWeapon> CurrentWeapon;

    // Input Handling
    UFUNCTION(BlueprintCallable, Category = "Input")
    void Move(const FInputActionValue& Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void Look(const FInputActionValue& Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartJump();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopJump();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartCrouch();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopCrouch();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartSprint();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopSprint();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartFire();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopFire();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartAltFire();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopAltFire();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void Reload();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void SwitchWeapon(const FInputActionValue& Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void Interact();

    // Weapon Management
    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void EquipWeapon(AHardlineWeapon* NewWeapon);

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void UnequipCurrentWeapon();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapons")
    AHardlineWeapon* GetCurrentWeapon() const { return CurrentWeapon; }

    // Health System Interface
    UFUNCTION(BlueprintCallable, Category = "Health")
    float GetHealth() const;

    UFUNCTION(BlueprintCallable, Category = "Health")
    float GetMaxHealth() const;

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsAlive() const;

    // Movement State
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Movement")
    bool IsSprinting() const { return bIsSprinting; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Movement")
    bool IsCrouching() const { return bIsCrouched; }

protected:
    // Movement State
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Movement")
    bool bIsSprinting = false;

    // Weapon Socket
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Weapons")
    FName WeaponSocketName = TEXT("WeaponSocket");

private:
    void UpdateMovementSpeed();
    void RegisterWithGameManager();
};
