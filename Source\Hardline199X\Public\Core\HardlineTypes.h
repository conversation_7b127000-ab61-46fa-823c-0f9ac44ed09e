#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "HardlineTypes.generated.h"

// Forward declarations
class AHardlineWeapon;
class AHardlineCharacter;
class UHardlineInventoryComponent;

/**
 * Core enums and data structures for Hardline199X
 */

UENUM(BlueprintType)
enum class EWeaponType : uint8
{
    None        UMETA(DisplayName = "None"),
    Shotgun     UMETA(DisplayName = "Shotgun"),
    Pistol      UMETA(DisplayName = "Pistol"),
    Rifle       UMETA(DisplayName = "Assault Rifle"),
    Melee       UMETA(DisplayName = "Melee"),
    Heavy       UMETA(DisplayName = "Heavy Weapon")
};

UENUM(BlueprintType)
enum class EDamageType : uint8
{
    None        UMETA(DisplayName = "None"),
    Ballistic   UMETA(DisplayName = "Ballistic"),
    Energy      UMETA(DisplayName = "Energy"),
    Explosive   UMETA(DisplayName = "Explosive"),
    Melee       UMETA(DisplayName = "Melee"),
    Environmental UMETA(DisplayName = "Environmental")
};

UENUM(BlueprintType)
enum class EGameState : uint8
{
    MainMenu    UMETA(DisplayName = "Main Menu"),
    Loading     UMETA(DisplayName = "Loading"),
    Playing     UMETA(DisplayName = "Playing"),
    Paused      UMETA(DisplayName = "Paused"),
    GameOver    UMETA(DisplayName = "Game Over"),
    Victory     UMETA(DisplayName = "Victory"),
    Settings    UMETA(DisplayName = "Settings")
};

UENUM(BlueprintType)
enum class EEnemyType : uint8
{
    None        UMETA(DisplayName = "None"),
    Goonbot     UMETA(DisplayName = "Goonbot"),
    Sniper      UMETA(DisplayName = "Sniper"),
    Heavy       UMETA(DisplayName = "Heavy"),
    Stealth     UMETA(DisplayName = "Stealth")
};

UENUM(BlueprintType)
enum class EObjectiveType : uint8
{
    None            UMETA(DisplayName = "None"),
    KillAll         UMETA(DisplayName = "Kill All Enemies"),
    Survive         UMETA(DisplayName = "Survive"),
    Reach           UMETA(DisplayName = "Reach Location"),
    Collect         UMETA(DisplayName = "Collect Items"),
    Hack            UMETA(DisplayName = "Hack Terminal"),
    Destroy         UMETA(DisplayName = "Destroy Target")
};

USTRUCT(BlueprintType)
struct HARDLINE199X_API FWeaponStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Damage = 25.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float FireRate = 600.0f; // Rounds per minute

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Range = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Accuracy = 0.95f; // 0.0 to 1.0

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    int32 MagazineSize = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float ReloadTime = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    EWeaponType WeaponType = EWeaponType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    EDamageType DamageType = EDamageType::Ballistic;

    FWeaponStats()
    {
        Damage = 25.0f;
        FireRate = 600.0f;
        Range = 1000.0f;
        Accuracy = 0.95f;
        MagazineSize = 30;
        ReloadTime = 2.0f;
        WeaponType = EWeaponType::None;
        DamageType = EDamageType::Ballistic;
    }
};

USTRUCT(BlueprintType)
struct HARDLINE199X_API FInventoryItem
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    FString ItemName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    int32 Quantity = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    EWeaponType ItemType = EWeaponType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    UTexture2D* ItemIcon = nullptr;

    FInventoryItem()
    {
        ItemName = TEXT("Unknown Item");
        Quantity = 1;
        ItemType = EWeaponType::None;
        ItemIcon = nullptr;
    }
};

USTRUCT(BlueprintType)
struct HARDLINE199X_API FObjectiveData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    FString ObjectiveName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    FString Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    EObjectiveType ObjectiveType = EObjectiveType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    bool bIsCompleted = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    bool bIsOptional = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    int32 TargetCount = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    int32 CurrentCount = 0;

    FObjectiveData()
    {
        ObjectiveName = TEXT("Unknown Objective");
        Description = TEXT("Complete this objective");
        ObjectiveType = EObjectiveType::None;
        bIsCompleted = false;
        bIsOptional = false;
        TargetCount = 1;
        CurrentCount = 0;
    }
};

USTRUCT(BlueprintType)
struct HARDLINE199X_API FPlayerStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Stats")
    int32 KillCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Stats")
    float CompletionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Stats")
    int32 ObjectivesCompleted = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Stats")
    float AccuracyPercentage = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Stats")
    int32 ShotsFired = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Stats")
    int32 ShotsHit = 0;

    FPlayerStats()
    {
        KillCount = 0;
        CompletionTime = 0.0f;
        ObjectivesCompleted = 0;
        AccuracyPercentage = 0.0f;
        ShotsFired = 0;
        ShotsHit = 0;
    }
};

// Delegate declarations for event system
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnHealthChanged, float, NewHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAmmoChanged, int32, NewAmmo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponChanged, EWeaponType, NewWeaponType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnObjectiveUpdated, const FObjectiveData&, ObjectiveData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnemyKilled, EEnemyType, EnemyType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGameStateChanged, EGameState, NewGameState);
