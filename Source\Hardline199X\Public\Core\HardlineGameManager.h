#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Core/HardlineTypes.h"
#include "HardlineGameManager.generated.h"

class AHardlineCharacter;
class AHardlineWeapon;

/**
 * Central Game Manager for Hardline199X
 * Coordinates between different game systems and manages game flow
 */
UCLASS(BlueprintType, Blueprintable)
class HARDLINE199X_API UHardlineGameManager : public UObject
{
    GENERATED_BODY()

public:
    UHardlineGameManager();

    // Initialization
    UFUNCTION(BlueprintCallable, Category = "Game Manager")
    void Initialize();

    UFUNCTION(BlueprintCallable, Category = "Game Manager")
    void Shutdown();

    // Player Management
    UFUNCTION(BlueprintCallable, Category = "Player Management")
    void RegisterPlayer(AHardlineCharacter* Player);

    UFUNCTION(BlueprintCallable, Category = "Player Management")
    AHardlineCharacter* GetPlayer() const { return PlayerCharacter; }

    // Objective Management
    UFUNCTION(BlueprintCallable, Category = "Objectives")
    void AddObjective(const FObjectiveData& NewObjective);

    UFUNCTION(BlueprintCallable, Category = "Objectives")
    void UpdateObjective(const FString& ObjectiveName, int32 Progress);

    UFUNCTION(BlueprintCallable, Category = "Objectives")
    void CompleteObjective(const FString& ObjectiveName);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Objectives")
    TArray<FObjectiveData> GetActiveObjectives() const { return ActiveObjectives; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Objectives")
    bool AreAllObjectivesComplete() const;

    // Enemy Management
    UFUNCTION(BlueprintCallable, Category = "Enemy Management")
    void RegisterEnemy(AActor* Enemy, EEnemyType EnemyType);

    UFUNCTION(BlueprintCallable, Category = "Enemy Management")
    void UnregisterEnemy(AActor* Enemy);

    UFUNCTION(BlueprintCallable, Category = "Enemy Management")
    void OnEnemyKilled(AActor* Enemy, EEnemyType EnemyType);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Enemy Management")
    int32 GetActiveEnemyCount() const { return ActiveEnemies.Num(); }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Enemy Management")
    TArray<AActor*> GetActiveEnemies() const { return ActiveEnemies; }

    // Weapon Management
    UFUNCTION(BlueprintCallable, Category = "Weapon Management")
    void RegisterWeapon(AHardlineWeapon* Weapon);

    UFUNCTION(BlueprintCallable, Category = "Weapon Management")
    void UnregisterWeapon(AHardlineWeapon* Weapon);

    // Game Flow
    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void StartLevel();

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void EndLevel(bool bVictory);

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void PauseGame();

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void ResumeGame();

    // Statistics
    UFUNCTION(BlueprintCallable, Category = "Statistics")
    void RecordKill(EEnemyType EnemyType);

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    void RecordShot(bool bHit);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Statistics")
    FPlayerStats GetCurrentStats() const { return CurrentStats; }

    // Event Delegates
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnObjectiveUpdated OnObjectiveUpdated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnEnemyKilled OnEnemyKilled;

protected:
    // Player Reference
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Player")
    TObjectPtr<AHardlineCharacter> PlayerCharacter;

    // Objectives
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Objectives")
    TArray<FObjectiveData> ActiveObjectives;

    // Enemy Tracking
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemies")
    TArray<AActor*> ActiveEnemies;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemies")
    TMap<AActor*, EEnemyType> EnemyTypeMap;

    // Weapon Tracking
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Weapons")
    TArray<AHardlineWeapon*> RegisteredWeapons;

    // Statistics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Statistics")
    FPlayerStats CurrentStats;

    // Game State
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Game State")
    bool bLevelInProgress = false;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Game State")
    float LevelStartTime = 0.0f;

private:
    void CheckLevelCompletion();
    void UpdateLevelTimer();
    FObjectiveData* FindObjective(const FString& ObjectiveName);
};
