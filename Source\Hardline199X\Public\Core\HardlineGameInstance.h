#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "Core/HardlineTypes.h"
#include "HardlineGameInstance.generated.h"

class UHardlineGameManager;
class UHardlineSaveGame;

/**
 * Main Game Instance for Hardline199X
 * Manages persistent data across levels and coordinates major systems
 */
UCLASS(BlueprintType, Blueprintable)
class HARDLINE199X_API UHardlineGameInstance : public UGameInstance
{
    GENERATED_BODY()

public:
    UHardlineGameInstance();

    virtual void Init() override;
    virtual void Shutdown() override;

    // Game Manager Access
    UFUNCTION(BlueprintCallable, Category = "Game Management")
    UHardlineGameManager* GetGameManager() const { return GameManager; }

    // Game State Management
    UFUNCTION(BlueprintCallable, Category = "Game State")
    void SetGameState(EGameState NewState);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game State")
    EGameState GetCurrentGameState() const { return CurrentGameState; }

    // Level Management
    UFUNCTION(BlueprintCallable, Category = "Level Management")
    void LoadLevel(const FString& LevelName);

    UFUNCTION(BlueprintCallable, Category = "Level Management")
    void RestartCurrentLevel();

    UFUNCTION(BlueprintCallable, Category = "Level Management")
    void ReturnToMainMenu();

    // Player Statistics
    UFUNCTION(BlueprintCallable, Category = "Player Stats")
    void UpdatePlayerStats(const FPlayerStats& NewStats);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Player Stats")
    FPlayerStats GetPlayerStats() const { return PlayerStats; }

    UFUNCTION(BlueprintCallable, Category = "Player Stats")
    void ResetPlayerStats();

    // Save/Load System
    UFUNCTION(BlueprintCallable, Category = "Save System")
    void SaveGame();

    UFUNCTION(BlueprintCallable, Category = "Save System")
    void LoadGame();

    UFUNCTION(BlueprintCallable, Category = "Save System")
    bool DoesSaveGameExist() const;

    // Settings Management
    UFUNCTION(BlueprintCallable, Category = "Settings")
    void ApplyGraphicsSettings(int32 QualityLevel);

    UFUNCTION(BlueprintCallable, Category = "Settings")
    void ApplyAudioSettings(float MasterVolume, float SFXVolume, float MusicVolume);

    // Event Delegates
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnGameStateChanged OnGameStateChanged;

protected:
    // Core Systems
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Core Systems")
    TObjectPtr<UHardlineGameManager> GameManager;

    // Game State
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Game State")
    EGameState CurrentGameState = EGameState::MainMenu;

    // Player Data
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Player Data")
    FPlayerStats PlayerStats;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Player Data")
    FString CurrentLevelName;

    // Save System
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Save System")
    TSubclassOf<UHardlineSaveGame> SaveGameClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Save System")
    FString SaveSlotName = TEXT("HardlineSave");

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Save System")
    int32 SaveUserIndex = 0;

    // Settings
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Settings")
    int32 GraphicsQuality = 3; // 0-4 scale

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Settings")
    float MasterVolume = 1.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Settings")
    float SFXVolume = 1.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Settings")
    float MusicVolume = 0.7f;

private:
    void InitializeGameManager();
    void LoadDefaultSettings();
    void SaveSettings();
};
