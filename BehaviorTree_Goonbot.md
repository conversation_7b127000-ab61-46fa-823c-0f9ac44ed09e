# AI — Goonbot (Behavior Tree + Perception, Blueprint-first, no EQS)

## Assets
- **Blackboard** `BB_Goonbot`
  - Keys:
    - `TargetActor` (Object)
    - `HasLOS` (Bool)
    - `HeardNoise` (Bool)
    - `LastKnownLocation` (Vector)
    - `PatrolCenter` (Vector)
- **Behavior Tree** `BT_Goonbot`
- **AIController** `BP_AC_Goonbot`
- **Character** `BP_Goonbot`
- **Perception**: add **AIPerception** on Controller with **Sight** + **Hearing**

## Perception Settings (Controller)
- **Sight**: Sight Radius 2200, Lose Sight 2600, FOV 110°, Max Age 5s, Detect Neutrals/Enemies
- **Hearing**: Hearing Range 2500, Use LOS No, Max Age 5s
- On Perception Updated:
  - If Stimulus == Sight & Sensed: set `TargetActor` and `HasLOS=true`
  - If Lost Sight: set `HasLOS=false`, set `LastKnownLocation` = last sensed location
  - If Stimulus == Hearing: set `HeardNoise=true`, `LastKnownLocation`=location

## Behavior Tree (high level)
- **Root → Selector**
  1) **Sequence: Engage**
     - **Blackboard** decorator: `HasLOS == true`
     - **Simple Parallel**
       - **Task: Move To** (TargetActor) (Acceptable Radius 400)
       - **Parallel: Shoot Service** (custom service that fires bursts while LOS true)
  2) **Sequence: Investigate Noise**
     - **Blackboard** decorator: `HeardNoise == true`
     - **Task: Move To** (LastKnownLocation, Acceptable Radius 200)
     - **Task: Wait** (0.8–1.2s)
     - **Task: Set Blackboard Value** `HeardNoise=false`
  3) **Sequence: Patrol**
     - **Task: Find Random Point in Radius** (around `PatrolCenter`, Radius 1200)
     - **Task: Move To** (that point)
     - **Task: Wait** (0.5–1.0s)

## Blueprint Notes
- **BP_Goonbot (Character)**
  - Add Health, HitReact montage; on damage from Shotgun alt-fire, set a short Stun tag to interrupt shooting (0.4s)
- **Shoot Service** (runs every 0.2–0.3s)
  - If `HasLOS` and distance < 1800 → play 3-round burst (add aim spread)
  - Otherwise do nothing

## Hooking Hearing to the Shotgun
- On shotgun Fire, call `Report Noise Event` at muzzle location with loudness ~1.0; AI Hearing will pick it up and set `HeardNoise`.
