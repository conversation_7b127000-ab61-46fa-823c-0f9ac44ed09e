# Hardline199X — Packaging Kit (UE5)

This folder contains:
- `Package_Win64_Shipping.bat` — one-click Windows Shipping build (edit paths).
- `Ship_Win64_GitHub_Actions.yml` — optional CI workflow.
- `UE_Editor_Setup.py` — Editor Python helper (folders + Nanite + default maps).
- `PCG_Graph_NeonSpillway.md` — PCG graph recipe for alley/substation/tunnels/dock.
- `MetaSounds_Shotgun.md` — Node list for THUMPER-12.
- `BehaviorTree_Goonbot.md` — AI setup (Blackboard, BT, Perception).

## Quick Start
1) **Install** UE 5.4+ and VS2022 (Game C++ + Desktop C++ + Windows SDK).
2) **Enable Plugins**: Enhanced Input, PCG, Python Editor Script (Editor → Plugins).
3) **Run UE_Editor_Setup.py** inside the Editor to scaffold folders and set default map (optional).
4) **Make sure your main map exists** at `/Game/Levels/NeonSpillway` or adjust in the script & Project Settings.
5) **Edit paths** at the top of `Package_Win64_Shipping.bat`:
   - UE install path, .uproject path, output directory.
6) **Double-click** the .bat. Your build lands under `...\Windows\Hardline199X.exe`.
7) **Zip & ship** that folder to testers.

## Tips
- If you still see debug messaging, ensure **Shipping** config and restart the editor after changing Packaging Settings.
- If RunUAT fails, 99% of the time it’s missing MSVC v143 or the Windows SDK; add via Visual Studio Installer.
- To narrow cooking, replace `-allmaps` with `-map=NeonSpillway+Substation+Tunnels+Dock`.
